def update_plot(new_energy, is_valid, amino_score=None, history_best_data=None):
    """实时更新监控图表"""
    energy_history.append(new_energy)

    # 有效能量记录
    if is_valid:
        valid_energy_history.append(new_energy)

    # 氨基酸评分处理
    if amino_score is not None and is_valid:
        amino_score_history.append(amino_score)

    # ===== 修复1：仅追加新的历史最优值 =====
    if history_best_data is not None and len(history_best_data) > 0:
        # 只有当新值大于当前最后一个值时才追加（保持非递减）
        if len(history_best_aas_list) == 0 or history_best_data[-1] > history_best_aas_list[-1]:
            history_best_aas_list.append(history_best_data[-1])

    # 动态调整坐标轴范围
    x_len = max(len(energy_history), len(amino_score_history))
    ax.set_xlim(0, max(50, x_len + 10))
    ax.set_ylim(1500, 3000)

    # ===== 修复2：调整右轴范围计算逻辑 =====
    all_amino = amino_score_history + history_best_aas_list
    current_max = max(all_amino[-200:]) if all_amino else 0
    ax2.set_ylim(0, max(100, current_max * 1.2))

    # 更新曲线数据
    line_all.set_data(range(len(energy_history)), energy_history)
    line_valid.set_data(range(len(valid_energy_history)), valid_energy_history)
    amino_line.set_data(range(len(amino_score_history)), amino_score_history)

    # ===== 修复3：生成阶梯状历史线 =====
    if len(history_best_aas_list) > 0:
        x_hist = []
        y_hist = []
        prev = 0
        for i, val in enumerate(history_best_aas_list):
            if val > prev:
                x_hist.extend([i, i + 1])
                y_hist.extend([prev, val])
                prev = val
        history_line.set_data(x_hist, y_hist)

    # 实时重绘
    fig.canvas.draw_idle()
    fig.canvas.flush_events()


def save_final_plot(final_amino):
    """保存最终结果图表"""
    plt.ioff()
    fig, ax = plt.subplots(figsize=(12, 6))
    ax2 = ax.twinx()

    # 绘制能量数据
    ax.set_title("模拟退火优化过程综合监控")
    ax.set_xlabel("尝试次数")
    ax.set_ylabel("总能量（千卡）")
    ax.axhspan(2160, 2640, color='lightgreen', alpha=0.3, label='有效区间')
    ax.plot(energy_history, 'gray', alpha=0.3, label='全部尝试')
    ax.plot(valid_energy_history, 'bo', markersize=3, label='有效解')

    # ===== 修复4：添加历史最优曲线到最终图 =====
    if len(history_best_aas_list) > 0:
        x_hist = []
        y_hist = []
        prev = 0
        for i, val in enumerate(history_best_aas_list):
            if val > prev:
                x_hist.extend([i, i + 1])
                y_hist.extend([prev, val])
                prev = val
        ax2.plot(x_hist, y_hist, 'gold', linestyle='-', linewidth=2,
                 marker='D', markersize=5, markeredgecolor='k',
                 label='历史最优评分')

    # 绘制当前解曲线
    ax2.plot(amino_score_history, 'r', marker='x', markersize=3,
             label='当前评分')

    # 标记最终结果
    final_energy = valid_energy_history[-1] if valid_energy_history else 0
    ax.plot(len(energy_history) - 1, final_energy, 'r*',
            markersize=10, label=f'最优能量({final_energy:.1f}千卡)')

    ax2.plot(len(history_best_aas_list) - 1, final_amino,
             marker='D', markersize=8, color='gold',
             markeredgecolor='k', linestyle='',
             label=f'最优评分({final_amino:.1f})')

    # 设置坐标轴范围
    ax.set_xlim(0, len(energy_history) + 10)
    ax2.set_ylim(0, max(100, final_amino * 1.2))

    # 合并图例
    lines, labels = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines + lines2, labels + labels2, loc='upper right', fontsize=8)

    plt.savefig('优化过程综合监控图.jpg', dpi=300, bbox_inches='tight')
    plt.close()


上面的函数是以前第1小问的适合的代码
很好，大体代码已经完成了最低标准，可运行，且有最终的结果
我需要完整的是第二小问，目标函数：根据模型计算得到的份数对应的解向量，给出用餐费用最低的男生一天膳食食谱
那么根据上面函数思想，改进我下面优化不是很好的函数，然后给出修改的函数的完整代码，把对应的氨基酸思想换成经济最低思想
（不要整个.py都给我输出了，我自己会修改，你提供明确的指引就行，我只需要完整的函数代码就行，其他小修正单独提出来我改正）


import numpy as np
import pandas as pd
import random
import math
import matplotlib.pyplot as plt

# 读取食物成分表数据
df = pd.read_excel("表3最终食堂成分表.xlsx")

# ================== 全局变量 ==================
energy_history = []
valid_energy_history = []
cost_history = []  # 当前解的费用记录
# 修改全局变量声明
history_best_cost = []
history_best_solution = None  # 历史最优解存储

plt.ion()
fig, ax = plt.subplots(figsize=(10, 5))
ax2 = ax.twinx()

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 初始化图形元素
ax.set_title("能量及费用实时监控")
ax.set_xlabel("尝试次数")
ax.set_ylabel("总能量（千卡）")
ax.axhspan(2160, 2640, color='lightgreen', alpha=0.3, label='有效区间')
line_all, = ax.plot([], [], 'gray', alpha=0.3, label='全部尝试')
line_valid, = ax.plot([], [], 'b', marker='o', label='有效解')
cost_line, = ax2.plot([], [], 'r', marker='x', label='当前费用')
history_line, = ax2.plot([], [], 'gold', linestyle='-', marker='D',
                         markersize=4, markeredgecolor='k', label='历史最优')

# 设置右轴属性
ax2.set_ylabel("费用（元）", color='r')
ax2.tick_params(axis='y', labelcolor='r')
ax2.set_ylim(0, 50)

# 合并图例
lines = [line_all, line_valid, cost_line, history_line]
labels = [l.get_label() for l in lines]
ax.legend(lines, labels, loc='upper left')

# 食物成分数据初始化
food_ids = df['ID'].values
food_names = df['食物名称'].values
prices = df['价格（元/份）'].values
half_serving = df['是否可半份'].values
meal_times = df['餐次'].values

# 营养成分数据
protein = df['蛋白质'].values
fat = df['脂肪'].values
carbs = df['碳水化合物'].values
fiber = df['膳食纤维'].values
calcium = df['钙'].values
iron = df['铁'].values
zinc = df['锌'].values
vitamin_A = df['维生素A'].values
vitamin_B1 = df['维生素B1'].values
vitamin_B2 = df['维生素B2'].values
vitamin_C = df['维生素C'].values

# 调整退火参数
T = 5000
alpha = 0.995
max_iter = 10000
min_temp = 1e-5

# 约束惩罚权重配置
PENALTY_WEIGHTS = {
    'energy_ratio': 5000,    # 产能营养素比例
    'nutrient': 0.1,        # 微量营养素
    'meal_ratio': 3000       # 餐次比
}

# 营养素单项权重配置
NUTRIENT_WEIGHTS = [
    60, 180, 50, 25, 120, 3, 15
]


# ================== 核心函数 ==================
def energy_constraint(solution):
    """能量硬约束检查"""
    total_energy = sum(
        (4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * quantity
        for i, quantity in enumerate(solution)
    )
    return 2160 <= total_energy <= 2640


def objective(solution, weights):
    """修改后的目标函数"""
    total_cost = sum(solution[i] * prices[i] for i in range(141))

    if not energy_constraint(solution):
        return (float('inf'), total_cost)

    # 使用动态权重计算惩罚
    penalty = 0
    violations = constraint_violation(solution)  # 需要修改该函数返回各分项

    penalty += sum(v * weights['energy_ratio'] for v in violations[:3])
    penalty += sum(v * weights['nutrient'] for v in violations[3:10])
    penalty += sum(v * weights['meal_ratio'] for v in violations[10:])

    return (total_cost + penalty, total_cost)


def get_temperature(iter):
    """三阶段温度调度"""
    if iter < 300:
        temp = 5000 * (0.9 ** iter)
    elif iter < 700:
        temp = 1000 * (0.95 ** (iter - 300))
    else:
        temp = 100 * (0.98 ** (iter - 700))
    return max(temp, 1e-3)


def calculate_total_energy(solution):
    """计算总能量"""
    return sum(
        (4*protein[i] + 9*fat[i] + 4*carbs[i] + 2*fiber[i]) * solution[i]
        for i in range(141)
    )


def constraint_violation(solution):
    """计算所有软约束的惩罚值，返回违反项列表"""
    violations = []

    # ========== 约束2：产能营养素比例 ==========
    p_energy = sum(4 * protein[i] * solution[i] for i in range(141))
    f_energy = sum(9 * fat[i] * solution[i] for i in range(141))
    c_energy = sum(4 * carbs[i] * solution[i] for i in range(141))
    total_energy = p_energy + f_energy + c_energy + sum(2 * fiber[i] * solution[i] for i in range(141))

    # 蛋白质比例
    protein_ratio = p_energy / total_energy
    violations.append(max(0, 0.10 - protein_ratio, protein_ratio - 0.15))

    # 脂肪比例
    fat_ratio = f_energy / total_energy
    violations.append(max(0, 0.20 - fat_ratio, fat_ratio - 0.30))

    # 碳水比例
    carbs_ratio = c_energy / total_energy
    violations.append(max(0, 0.50 - carbs_ratio, carbs_ratio - 0.65))

    # ========== 约束3：微量营养素 ==========
    nutrients = [
        sum(calcium[i] * solution[i] for i in range(141)),  # 钙
        sum(iron[i] * solution[i] for i in range(141)),  # 铁
        sum(zinc[i] * solution[i] for i in range(141)),  # 锌
        sum(vitamin_A[i] * solution[i] for i in range(141)),  # 维生素A
        sum(vitamin_B1[i] * solution[i] for i in range(141)),  # 维生素B1
        sum(vitamin_B2[i] * solution[i] for i in range(141)),  # 维生素B2
        sum(vitamin_C[i] * solution[i] for i in range(141))  # 维生素C
    ]
    ref_values = [800, 12, 12.5, 800, 1.4, 1.4, 100]
    for val, ref in zip(nutrients, ref_values):
        rel_error = 0
        if val < 0.9 * ref:
            rel_error = (0.9 * ref - val) / ref
        elif val > 1.1 * ref:
            rel_error = (val - 1.1 * ref) / ref
        violations.append(rel_error)

    # ========== 约束4：餐次比 ==========
    breakfast_e = sum((4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i] for i in range(33))
    lunch_e = sum((4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i] for i in range(33, 92))
    dinner_e = sum((4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i] for i in range(92, 141))
    total_e = breakfast_e + lunch_e + dinner_e

    # 早餐比例
    breakfast_ratio = breakfast_e / total_e
    violations.append(max(0, 0.25 - breakfast_ratio, breakfast_ratio - 0.35))

    # 午餐比例
    lunch_ratio = lunch_e / total_e
    violations.append(max(0, 0.30 - lunch_ratio, lunch_ratio - 0.40))

    # 晚餐比例
    dinner_ratio = dinner_e / total_e
    violations.append(max(0, 0.30 - dinner_ratio, dinner_ratio - 0.40))

    return violations  # 直接返回违反项列表


def initialize_solution():
    """生成满足能量约束的初始解"""
    global energy_history, valid_energy_history
    energy_history = []
    valid_energy_history = []

    max_attempts = 1000
    for _ in range(max_attempts):
        solution = np.zeros(141, dtype=int)

        # 各餐随机选择食物
        for meal_range in [range(33), range(33, 92), range(92, 141)]:
            selected = random.sample(list(meal_range), random.randint(3, 5))
            for i in selected:
                solution[i] = random.randint(1, 2)

        # 计算能量
        total_energy = sum(
            (4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i]
            for i in range(141))

        if 2160 <= total_energy <= 2640:
            update_plot(total_energy, True, sum(solution * prices))
            return solution

        update_plot(total_energy, False)

    raise ValueError("无法生成有效初始解")


def update_plot(energy, is_valid, current_cost=None, history_best=None):
    """更新实时监控图表"""
    energy_history.append(energy)

    if is_valid:
        valid_energy_history.append(energy)
        if current_cost is not None:
            cost_history.append(current_cost)
            if history_best is not None:
                if not history_best_cost or current_cost < min(history_best_cost):
                    history_best_cost.append(current_cost)

    # 动态调整坐标轴
    x_len = max(len(energy_history), len(cost_history))
    ax.set_xlim(0, x_len + 10)
    ax.set_ylim(1500, 3000)

    # 更新曲线数据
    line_all.set_data(range(len(energy_history)), energy_history)
    line_valid.set_data(range(len(valid_energy_history)), valid_energy_history)
    cost_line.set_data(range(len(cost_history)), cost_history)

    # 更新历史最优线
    if history_best_cost:
        x_hist = []
        y_hist = []
        prev_cost = float('inf')
        for i, cost in enumerate(history_best_cost):
            if cost < prev_cost:
                x_hist.extend([i, i + 1])
                y_hist.extend([prev_cost, cost])
                prev_cost = cost
        history_line.set_data(x_hist, y_hist)
        ax2.set_ylim(0, max(cost_history) * 1.2 if cost_history else 50)

    fig.canvas.draw_idle()
    fig.canvas.flush_events()


def save_final_plot(final_cost):
    """保存最终结果图表"""
    plt.ioff()
    fig, ax = plt.subplots(figsize=(12, 6))
    ax2 = ax.twinx()

    ax.plot(energy_history, 'gray', alpha=0.3, label='全部尝试')
    ax.plot(valid_energy_history, 'bo', markersize=3, label='有效解')
    ax2.plot(cost_history, 'r', marker='x', label='当前费用')

    if history_best_cost:
        x_hist = []
        y_hist = []
        prev = float('inf')
        for i, cost in enumerate(history_best_cost):
            if cost < prev:
                x_hist.extend([i, i + 1])
                y_hist.extend([prev, cost])
                prev = cost
        ax2.plot(x_hist, y_hist, 'gold', marker='D', markersize=5, label='历史最优')

    ax.set_title("优化过程监控")
    ax.set_xlabel("迭代次数")
    ax.set_ylabel("能量（千卡）")
    ax2.set_ylabel("费用（元）", color='r')
    ax.legend(loc='upper left')
    ax2.legend(loc='upper right')
    plt.savefig('优化结果.jpg', dpi=300)
    plt.close()


def simulated_annealing():
    """动态权重调整的改进退火算法（费用优化版）"""
    # 初始化动态惩罚权重
    adaptive_weights = {
        'energy_ratio': 5000,
        'nutrient': 0.1,
        'meal_ratio': 3000
    }

    current = initialize_solution()
    best = current.copy()
    global history_best_solution
    history_best_solution = current.copy()

    current_score, current_cost = objective(current, adaptive_weights)
    best_score, best_cost = current_score, current_cost
    history_best_cost.append(current_cost)

    no_improve_counter = 0  # 记录未改进次数

    for iter in range(max_iter):
        # 动态调整惩罚权重（核心改进）
        if iter > 500 and no_improve_counter > 50:
            # 连续未改进时降低能量和餐次惩罚
            adaptive_weights['energy_ratio'] *= 0.85
            adaptive_weights['meal_ratio'] *= 0.7
            adaptive_weights['nutrient'] *= 1.15  # 保持微量营养约束

        temp = get_temperature(iter)
        if temp < 1e-5:
            break

        # 生成价格导向的邻域解
        neighbor = neighbor_solution(current, iter)
        neighbor_score, neighbor_cost = objective(neighbor, adaptive_weights)

        # 更新历史最优
        if neighbor_cost < history_best_cost[-1] and energy_constraint(neighbor):
            history_best_cost.append(neighbor_cost)
            history_best_solution = neighbor.copy()
            no_improve_counter = 0  # 重置计数器
        else:
            no_improve_counter += 1

        # 退火接受准则
        delta = neighbor_score - current_score
        if delta < 0 or random.random() < math.exp(-delta / (temp + 1e-10)):
            current = neighbor
            current_score = neighbor_score
            current_cost = neighbor_cost

            if current_score < best_score:
                best = current.copy()
                best_score = current_score
                best_cost = current_cost

        # 每500次进行记忆重启
        if iter % 500 == 0 and history_best_cost:
            current = history_best_solution.copy()
            current_score, current_cost = objective(current, adaptive_weights)
            print(f"迭代{iter}: 重启到历史最优 | 当前费用:{current_cost:.2f}元")

        # 动态更新约束权重（根据进度）
        adaptive_weights['nutrient'] *= 1.001  # 微量营养约束逐渐收紧

    # 精细化局部搜索（使用最终权重）
    print("\n进入二次优化阶段（权重稳定）:")
    for _ in range(1000):
        temp_sol = neighbor_solution(history_best_solution, 1000)
        temp_score, temp_cost = objective(temp_sol, adaptive_weights)

        if temp_cost < history_best_cost[-1]:
            history_best_solution = temp_sol.copy()
            history_best_cost.append(temp_cost)

    return history_best_solution, history_best_cost[-1]


def calculate_energy_components(solution):
    """计算总能量及各供能成分"""
    p_energy = sum(4 * protein[i] * solution[i] for i in range(141))
    f_energy = sum(9 * fat[i] * solution[i] for i in range(141))
    c_energy = sum(4 * carbs[i] * solution[i] for i in range(141))
    fiber_energy = sum(2 * fiber[i] * solution[i] for i in range(141))
    total_energy = p_energy + f_energy + c_energy + fiber_energy
    return p_energy, f_energy, c_energy, total_energy


def calculate_nutrients(solution):
    """计算非产能营养素总量"""
    calcium_t = sum(calcium[i] * solution[i] for i in range(141))
    iron_t = sum(iron[i] * solution[i] for i in range(141))
    zinc_t = sum(zinc[i] * solution[i] for i in range(141))
    vitA_t = sum(vitamin_A[i] * solution[i] for i in range(141))
    vitB1_t = sum(vitamin_B1[i] * solution[i] for i in range(141))
    vitB2_t = sum(vitamin_B2[i] * solution[i] for i in range(141))
    vitC_t = sum(vitamin_C[i] * solution[i] for i in range(141))
    return (calcium_t, iron_t, zinc_t, vitA_t, vitB1_t, vitB2_t, vitC_t)


def calculate_meal_ratios(solution):
    """计算餐次能量占比"""
    breakfast_e = sum((4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i] for i in range(33))
    lunch_e = sum((4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i] for i in range(33, 92))
    dinner_e = sum((4 * protein[i] + 9 * fat[i] + 4 * carbs[i] + 2 * fiber[i]) * solution[i] for i in range(92, 141))
    total_e = breakfast_e + lunch_e + dinner_e
    return breakfast_e / total_e, lunch_e / total_e, dinner_e / total_e


def neighbor_solution(current, iter):
    """价格优化导向的邻域生成"""
    new = current.copy()

    # 按迭代进度调整探索强度
    if iter < 2000:  # 前期积极替换高价菜品
        expensive = sorted(range(141), key=lambda x: prices[x], reverse=True)[:30]
        for i in random.sample(expensive, 2):
            new[i] = max(0, new[i] - 1)
    else:  # 后期精细调整
        cheap = sorted(range(141), key=lambda x: prices[x])[:50]
        for i in random.sample(cheap, 2):
            new[i] += 1

    # 确保各餐有食物（同原逻辑）
    return new


# ================== 主程序 ==================
if __name__ == "__main__":
    # 运行退火算法
    best_solution, best_cost = simulated_annealing()

    # 计算最终能量和营养成分
    p_energy, f_energy, c_energy, total_energy = calculate_energy_components(best_solution)
    breakfast_ratio, lunch_ratio, dinner_ratio = calculate_meal_ratios(best_solution)
    calcium_t, iron_t, zinc_t, vitA_t, vitB1_t, vitB2_t, vitC_t = calculate_nutrients(best_solution)

    # 处理实际份数计算
    actual_quantities = [
        qty * 0.5 if half == '是' else qty
        for qty, half in zip(best_solution, half_serving)
    ]
    total_cost = sum(q * p for q, p in zip(actual_quantities, prices))

    # 创建结果DataFrame
    result_df = pd.DataFrame({
        'ID': food_ids,
        '食物名称': food_names,
        '价格（元/份）': prices,
        '是否可半份': half_serving,
        '餐次': meal_times,
        '计算份数': best_solution,
        '实际份数': actual_quantities,
        '小计费用': [q * p for q, p in zip(actual_quantities, prices)]
    })

    # 创建汇总信息
    REFERENCE_VALUES = {
        '钙': 800, '铁': 12, '锌': 12.5,
        '维生素A': 800, '维生素B1': 1.4,
        '维生素B2': 1.4, '维生素C': 100
    }

    summary_data = [
        {'食物名称': '=== 基础能量约束 ===', '小计费用': ''},
        {'食物名称': f'总能量：{total_energy:.2f}千卡（标准：2160-2640）', '小计费用': ''},

        {'食物名称': '\n=== 产能营养素占比 ===', '小计费用': ''},
        {'食物名称': f'蛋白质：{p_energy / total_energy * 100:.2f}%（10-15%）', '小计费用': ''},
        {'食物名称': f'脂肪：{f_energy / total_energy * 100:.2f}%（20-30%）', '小计费用': ''},
        {'食物名称': f'碳水：{c_energy / total_energy * 100:.2f}%（50-65%）', '小计费用': ''},

        {'食物名称': '\n=== 微量营养素 ===', '小计费用': ''},
        {'食物名称': f'钙：{calcium_t:.2f}/{REFERENCE_VALUES["钙"]}', '小计费用': ''},
        {'食物名称': f'铁：{iron_t:.2f}/{REFERENCE_VALUES["铁"]}', '小计费用': ''},
        {'食物名称': f'锌：{zinc_t:.2f}/{REFERENCE_VALUES["锌"]}', '小计费用': ''},
        {'食物名称': f'维A：{vitA_t:.2f}/{REFERENCE_VALUES["维生素A"]}', '小计费用': ''},
        {'食物名称': f'维B1：{vitB1_t:.2f}/{REFERENCE_VALUES["维生素B1"]}', '小计费用': ''},
        {'食物名称': f'维B2：{vitB2_t:.2f}/{REFERENCE_VALUES["维生素B2"]}', '小计费用': ''},
        {'食物名称': f'维C：{vitC_t:.2f}/{REFERENCE_VALUES["维生素C"]}', '小计费用': ''},

        {'食物名称': '\n=== 餐次比 ===', '小计费用': ''},
        {'食物名称': f'早餐：{breakfast_ratio * 100:.2f}%（25-35%）', '小计费用': ''},
        {'食物名称': f'午餐：{lunch_ratio * 100:.2f}%（30-40%）', '小计费用': ''},
        {'食物名称': f'晚餐：{dinner_ratio * 100:.2f}%（30-40%）', '小计费用': ''},

        {'食物名称': '\n=== 费用汇总 ===', '小计费用': ''},
        {'食物名称': '总费用', '小计费用': f'{total_cost:.2f}元'}
    ]

    # 添加汇总行
    summary_df = pd.DataFrame(summary_data)
    result_df = pd.concat([result_df, summary_df], ignore_index=True)
    result_df.to_excel("男生最低费用日食谱.xlsx", index=False)

    # 控制台输出
    print("\n" + "=" * 50)
    print(" 男生日食谱优化结果 ".center(50, '='))
    print("=" * 50)

    print(f"\n最优总费用：{total_cost:.2f}元")
    print(f"总能量摄入：{total_energy:.2f}kcal ({2160}-{2640}kcal)")

    print("\n[营养指标达成情况]")
    print(f"蛋白质供能：{p_energy / total_energy * 100:.2f}% (标准10-15%)")
    print(f"脂肪供能：{f_energy / total_energy * 100:.2f}% (标准20-30%)")
    print(f"碳水供能：{c_energy / total_energy * 100:.2f}% (标准50-65%)")

    print("\n[微量营养素摄入]")
    print(f"钙：{calcium_t:.2f}/{REFERENCE_VALUES['钙']} ({calcium_t / REFERENCE_VALUES['钙'] * 100:.1f}%)")
    print(f"铁：{iron_t:.2f}/{REFERENCE_VALUES['铁']} ({iron_t / REFERENCE_VALUES['铁'] * 100:.1f}%)")
    print(f"锌：{zinc_t:.2f}/{REFERENCE_VALUES['锌']} ({zinc_t / REFERENCE_VALUES['锌'] * 100:.1f}%)")
    print(f"维生素C：{vitC_t:.2f}/{REFERENCE_VALUES['维生素C']} ({vitC_t / REFERENCE_VALUES['维生素C'] * 100:.1f}%)")

    print("\n[餐次分配]")
    print(f"早餐：{breakfast_ratio * 100:.2f}% (标准25-35%)")
    print(f"午餐：{lunch_ratio * 100:.2f}% (标准30-40%)")
    print(f"晚餐：{dinner_ratio * 100:.2f}% (标准30-40%)")
    print("=" * 50)